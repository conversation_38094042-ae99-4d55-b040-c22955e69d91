{"ConnectionStrings": {"DefaultConnection": "Server=localhost;Database=YendorCats;User=root;Password=${MYSQL_PASSWORD:-password};Port=3306;", "ProductionConnection": "Server=db;Database=YendorCats;User=${MYSQL_USER};Password=${MYSQL_PASSWORD};Port=3306;", "SqliteConnection": "Data Source=Data/yendorcats.db"}, "AWS": {"Region": "us-west-004", "UseCredentialsFromSecrets": true, "S3": {"BucketName": "yendor", "UseDirectS3Urls": true, "ServiceUrl": "https://s3.us-west-004.backblazeb2.com", "PublicUrl": "https://f004.backblazeb2.com/file/yendor/{key}", "UseCdn": false, "CdnDomain": "", "AccessKey": "${AWS_S3_ACCESS_KEY}", "SecretKey": "${AWS_S3_SECRET_KEY}"}}, "HybridStorage": {"DefaultProvider": "S3", "EnableDualStorage": true, "S3": {"BucketName": "yendor", "Region": "us-west-004", "ServiceUrl": "https://s3.us-west-004.backblazeb2.com", "PublicUrl": "https://f004.backblazeb2.com/file/yendor/{key}", "UseDirectUrls": true, "UseCdn": false, "CdnDomain": ""}, "B2": {"BucketName": "yendorcats-b2", "Region": "us-west-004", "ServiceUrl": "https://s3.us-west-004.backblazeb2.com", "PublicUrl": "https://f004.backblazeb2.com/file/yendorcats-b2/{key}", "UseDirectUrls": true, "UseCdn": false, "CdnDomain": "", "ApplicationKeyId": "${B2_APPLICATION_KEY_ID}", "ApplicationKey": "${B2_APPLICATION_KEY}", "BucketId": "${B2_BUCKET_ID}"}, "Migration": {"BatchSize": 100, "MaxRetries": 3, "RetryDelaySeconds": 30, "EnableProgressLogging": true, "ValidateAfterMigration": true}, "Performance": {"CacheWarmupOnStartup": true, "PreloadPopularImages": true, "MaxConcurrentOperations": 10, "ThumbnailSizes": ["Small", "Medium", "Large"]}}, "JwtSettings": {"Secret": "${YENDOR_JWT_SECRET}", "Issuer": "YendorCatsApi", "Audience": "YendorCatsClients", "ExpiryMinutes": 60, "RefreshExpiryDays": 7}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "Serilog": {"MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "WriteTo": [{"Name": "<PERSON><PERSON><PERSON>"}, {"Name": "File", "Args": {"path": "Logs/log-.txt", "rollingInterval": "Day"}}]}}